# ARB-AIO

A Bun monorepo project with Hono backend and Next.js frontend.

## Project Structure

```
arb-aio/
├── packages/
│   ├── backend/          # Hono API server
│   ├── frontend/         # Next.js web application
│   ├── a/               # Legacy package A
│   └── b/               # Legacy package B
├── package.json         # Root workspace configuration
└── README.md           # This file
```

## Quick Start

### Prerequisites

- [Bun](https://bun.sh/) installed on your system

### Installation

```bash
# Install all dependencies
bun run install:all
```

### Development

```bash
# Start both backend and frontend in development mode
bun run dev

# Or start them individually:
bun run dev:backend    # Starts Hono server on http://localhost:8000
bun run dev:frontend   # Starts Next.js on http://localhost:3000
```

### Production Build

```bash
# Build both projects
bun run build

# Start both in production mode
bun run start
```

### Individual Package Commands

```bash
# Backend (Hono)
cd packages/backend
bun install
bun run dev          # Development server
bun run build        # Build for production
bun run start        # Production server

# Frontend (Next.js)
cd packages/frontend
bun install
bun run dev          # Development server
bun run build        # Build for production
bun run start        # Production server
```

## Tech Stack

### Backend
- **[Hono](https://hono.dev/)** - Fast, lightweight web framework
- **[Bun](https://bun.sh/)** - JavaScript runtime and package manager
- **TypeScript** - Type safety

### Frontend
- **[Next.js 14](https://nextjs.org/)** - React framework with App Router
- **[React 18](https://reactjs.org/)** - UI library
- **[SWR](https://swr.vercel.app/)** - Data fetching with caching and revalidation
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **TypeScript** - Type safety

## API Endpoints

The backend provides the following endpoints:

- `GET /` - Welcome message and API info
- `GET /health` - Health check
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user

## SWR Features

- **Automatic Caching**: Responses are cached automatically for better performance
- **Background Revalidation**: Data is revalidated in the background to stay fresh
- **Error Retry**: Automatic retry on failed requests with exponential backoff
- **Focus Revalidation**: Data refreshes when the user refocuses the tab
- **Network Recovery**: Automatic revalidation when network connection is restored
- **Optimistic Updates**: UI updates immediately with local mutations
- **Custom Hooks**: Reusable hooks for different API endpoints

## Development Notes

- **SWR Integration**: All API calls use SWR for optimal data fetching
- **Custom Hooks**: Dedicated hooks for different API operations
- **Error Handling**: Comprehensive error handling with user feedback
- **Loading States**: Proper loading indicators for all operations
- **Hot Reload**: Both development servers support hot reloading
- **CORS**: Configured for seamless frontend-backend communication

## Scripts

- `bun run dev` - Start both backend and frontend in development mode
- `bun run build` - Build both projects for production
- `bun run start` - Start both projects in production mode
- `bun run clean` - Clean all node_modules and lock files
- `bun run install:all` - Install dependencies for all packages
