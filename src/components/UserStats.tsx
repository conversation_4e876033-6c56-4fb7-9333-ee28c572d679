'use client'

import { useUsers } from '@/hooks/useApi'

export default function UserStats() {
  const { users, isLoading, isError } = useUsers()

  if (isLoading) {
    return (
      <div className="p-4 bg-white/10 rounded-lg backdrop-blur-sm">
        <h3 className="text-lg font-semibold mb-2">User Statistics</h3>
        <div className="animate-pulse">
          <div className="h-4 bg-white/20 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-white/20 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="p-4 bg-white/10 rounded-lg backdrop-blur-sm">
        <h3 className="text-lg font-semibold mb-2">User Statistics</h3>
        <p className="text-red-400 text-sm">Failed to load statistics</p>
      </div>
    )
  }

  const totalUsers = users.length
  const domains = users.reduce((acc, user) => {
    const domain = user.email.split('@')[1]
    acc[domain] = (acc[domain] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const topDomain = Object.entries(domains).sort(([,a], [,b]) => b - a)[0]

  return (
    <div className="p-4 bg-white/10 rounded-lg backdrop-blur-sm">
      <h3 className="text-lg font-semibold mb-3">User Statistics (Live)</h3>
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>Total Users:</span>
          <span className="font-medium">{totalUsers}</span>
        </div>
        {topDomain && (
          <div className="flex justify-between">
            <span>Top Domain:</span>
            <span className="font-medium">{topDomain[0]} ({topDomain[1]})</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>Unique Domains:</span>
          <span className="font-medium">{Object.keys(domains).length}</span>
        </div>
        <div className="text-xs text-gray-400 mt-3">
          ✨ Updates automatically with SWR
        </div>
      </div>
    </div>
  )
}
