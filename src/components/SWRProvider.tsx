'use client'

import { SWRConfig } from 'swr'
import { fetcher } from '@/lib/api'

interface SWRProviderProps {
  children: React.ReactNode
}

export default function SWRProvider({ children }: SWRProviderProps) {
  return (
    <SWRConfig
      value={{
        fetcher,
        errorRetryCount: 3,
        errorRetryInterval: 1000,
        dedupingInterval: 2000,
        focusThrottleInterval: 5000,
        onError: (error, key) => {
          console.error('SWR Error:', error, 'Key:', key)
        },
        onErrorRetry: (error, key, config, revalidate, { retryCount }) => {
          // Never retry on 404
          if (error.status === 404) return

          // Never retry on 401 (unauthorized)
          if (error.status === 401) return

          // Only retry up to 3 times
          if (retryCount >= 3) return

          // Retry after 1 second
          setTimeout(() => revalidate({ retryCount }), 1000)
        },
      }}
    >
      {children}
    </SWRConfig>
  )
}
