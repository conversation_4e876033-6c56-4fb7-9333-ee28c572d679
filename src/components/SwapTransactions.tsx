'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSwapTransactions } from '@/hooks/useApi'
import type { SwapTransaction } from '@/lib/api'
import ArbitrageAnalysisComponent from './ArbitrageAnalysis'

export default function SwapTransactions() {
  const [address, setAddress] = useState('')
  const [searchAddress, setSearchAddress] = useState<string | null>(null)
  const [enableArbitrage, setEnableArbitrage] = useState(false)
  const router = useRouter()

  const { transactions, arbitrageSummary, isLoading, isError } = useSwapTransactions(searchAddress, enableArbitrage)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (!address.trim()) return
    setSearchAddress(address.trim())
  }

  const formatTimestamp = (timestamp: string) => {
    // Convert Unix timestamp (seconds) to milliseconds for JavaScript Date
    const timestampMs = parseInt(timestamp) * 1000
    return new Date(timestampMs).toLocaleString()
  }

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount)
  }

  const formatTokenAmount = (amount: string, decimals = 6) => {
    const num = parseFloat(amount)
    if (isNaN(num)) return amount
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: decimals,
      signDisplay: 'always'
    }).format(num)
  }

  const formatPrice = (price: number, decimals = 8) => {
    if (isNaN(price) || !isFinite(price)) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: decimals,
    }).format(price)
  }

  const getAmountColor = (amount: string) => {
    const num = parseFloat(amount)
    if (isNaN(num)) return 'text-gray-900'
    return num >= 0 ? 'text-green-600' : 'text-red-600'
  }



  const truncateHash = (hash: string, length = 10) => {
    if (hash.length <= length * 2) return hash
    return `${hash.slice(0, length)}...${hash.slice(-length)}`
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Swap Transactions Query
        </h2>

        {/* Search Form */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                Wallet Address
              </label>
              <input
                type="text"
                id="address"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                placeholder="Enter wallet address (e.g., ******************************************)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex items-center gap-2 mb-4">
              <input
                type="checkbox"
                id="enableArbitrage"
                checked={enableArbitrage}
                onChange={(e) => setEnableArbitrage(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="enableArbitrage" className="text-sm text-gray-700">
                Enable arbitrage analysis (compares with Binance prices - may take longer)
              </label>
            </div>
            <div className="flex items-end">
              <button
                type="submit"
                disabled={!address.trim() || isLoading}
                className="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>
        </form>

        {/* Results */}
        {searchAddress && (
          <div>
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Results for: <span className="font-mono text-sm">{truncateHash(searchAddress, 15)}</span>
              </h3>
            </div>

            {/* Arbitrage Summary */}
            {arbitrageSummary && (
              <div className="mb-6">
                <ArbitrageAnalysisComponent summary={arbitrageSummary} showSummary={true} />
              </div>
            )}

            {isError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <div className="flex">
                  <div className="text-red-800">
                    <h3 className="text-sm font-medium">Error loading transactions</h3>
                    <p className="text-sm mt-1">
                      Failed to fetch swap transactions. Please check the address and try again.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {isLoading && (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-gray-600">Loading transactions...</span>
              </div>
            )}

            {!isLoading && !isError && transactions.length === 0 && (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  <p className="text-lg font-medium">No transactions found</p>
                  <p className="text-sm mt-1">
                    No swap transactions found for this address.
                  </p>
                </div>
              </div>
            )}

            {!isLoading && !isError && transactions.length > 0 && (
              <>
                <div className="mb-4 flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    Found {transactions.length} transaction{transactions.length !== 1 ? 's' : ''} for address: {' '}
                    <span className="font-mono">{truncateHash(searchAddress || '', 8)}</span>
                  </p>
                  <button
                    onClick={() => router.push(`/swaps/${searchAddress}`)}
                    className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    View Full Page
                  </button>
                </div>

                <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Transaction Hash
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Timestamp
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IOTX Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        SOL Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IOTX/SOL Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount (USD)
                      </th>
                      {enableArbitrage && (
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Arbitrage
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transactions.map((tx: SwapTransaction, index: number) => (
                      <tr key={`${tx.transaction}-${index}`} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-mono text-gray-900">
                            {truncateHash(tx.transaction)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatTimestamp(tx.timestamp)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm font-medium ${getAmountColor(tx.iotx_amount)}`}>
                            {formatTokenAmount(tx.iotx_amount)} IOTX
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm font-medium ${getAmountColor(tx.sol_amount)}`}>
                            {formatTokenAmount(tx.sol_amount)} SOL
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-600">
                            {formatPrice(tx.iotx_per_sol)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatAmount(tx.amount_usd)}
                          </div>
                        </td>
                        {enableArbitrage && (
                          <td className="px-6 py-4 whitespace-nowrap">
                            {tx.arbitrage && (
                              <ArbitrageAnalysisComponent analysis={tx.arbitrage} showSummary={false} />
                            )}
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
