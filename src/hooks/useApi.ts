import useSWR from 'swr'
import useSWRMutation from 'swr/mutation'
import {
  fetcher,
  API_ENDPOINTS,
  apiMutations,
  type ApiResponse,
  type UsersResponse,
  type SwapTransactionsResponse
} from '@/lib/api'

// Hook for API info
export function useApiInfo() {
  const { data, error, isLoading } = useSWR<ApiResponse>(
    API_ENDPOINTS.apiInfo,
    fetcher,
    {
      refreshInterval: 30000, // Refresh every 30 seconds
      revalidateOnFocus: false,
    }
  )

  return {
    apiInfo: data,
    isLoading,
    isError: error,
  }
}

// Hook for users list
export function useUsers() {
  const { data, error, isLoading, mutate } = useSWR<UsersResponse>(
    API_ENDPOINTS.users,
    fetcher,
    {
      refreshInterval: 10000, // Refresh every 10 seconds
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
    }
  )

  return {
    users: data?.users || [],
    isLoading,
    isError: error,
    mutate, // For manual revalidation
  }
}

// Hook for single user
export function useUser(id: number | null) {
  const { data, error, isLoading } = useSWR(
    id ? API_ENDPOINTS.user(id) : null,
    fetcher
  )

  return {
    user: data?.user,
    isLoading,
    isError: error,
  }
}

// Hook for swap transactions by address
export function useSwapTransactions(address: string | null, withArbitrage: boolean = false) {
  const { data, error, isLoading, mutate } = useSWR<SwapTransactionsResponse>(
    address ? API_ENDPOINTS.swapTxs(address, withArbitrage) : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      // Increase timeout for arbitrage analysis
      errorRetryInterval: withArbitrage ? 5000 : 1000,
    }
  )

  return {
    transactions: data?.list || [],
    arbitrageSummary: data?.arbitrage_summary,
    isLoading,
    isError: error,
    mutate,
  }
}

// Mutation hooks
export function useCreateUser() {
  const { trigger, isMutating, error } = useSWRMutation(
    API_ENDPOINTS.users,
    async (url: string, { arg }: { arg: { name: string; email: string } }) => {
      return apiMutations.createUser(arg)
    }
  )

  return {
    createUser: trigger,
    isCreating: isMutating,
    error,
  }
}

export function useUpdateUser() {
  const { trigger, isMutating, error } = useSWRMutation(
    API_ENDPOINTS.users,
    async (url: string, { arg }: { arg: { id: number; name: string; email: string } }) => {
      return apiMutations.updateUser(arg.id, { name: arg.name, email: arg.email })
    }
  )

  return {
    updateUser: trigger,
    isUpdating: isMutating,
    error,
  }
}

export function useDeleteUser() {
  const { trigger, isMutating, error } = useSWRMutation(
    API_ENDPOINTS.users,
    async (url: string, { arg }: { arg: { id: number } }) => {
      return apiMutations.deleteUser(arg.id)
    }
  )

  return {
    deleteUser: trigger,
    isDeleting: isMutating,
    error,
  }
}
