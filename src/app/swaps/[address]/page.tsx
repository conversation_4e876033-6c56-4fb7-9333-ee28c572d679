'use client'

import { useParams } from 'next/navigation'
import { useSwapTransactions } from '@/hooks/useApi'
import type { SwapTransaction } from '@/lib/api'
import Link from 'next/link'

export default function AddressSwapsPage() {
  const params = useParams()
  const address = params.address as string
  
  const { transactions, isLoading, isError } = useSwapTransactions(address)

  const formatTimestamp = (timestamp: string) => {
    // Convert Unix timestamp (seconds) to milliseconds for JavaScript Date
    const timestampMs = parseInt(timestamp) * 1000
    return new Date(timestampMs).toLocaleString()
  }

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount)
  }

  const formatTokenAmount = (amount: string, decimals = 6) => {
    const num = parseFloat(amount)
    if (isNaN(num)) return amount
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: decimals,
      signDisplay: 'always'
    }).format(num)
  }

  const formatPrice = (price: number, decimals = 8) => {
    if (isNaN(price) || !isFinite(price)) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: decimals,
    }).format(price)
  }

  const getAmountColor = (amount: string) => {
    const num = parseFloat(amount)
    if (isNaN(num)) return 'text-gray-900'
    return num >= 0 ? 'text-green-600' : 'text-red-600'
  }

  const truncateHash = (hash: string, length = 10) => {
    if (hash.length <= length * 2) return hash
    return `${hash.slice(0, length)}...${hash.slice(-length)}`
  }

  const truncateAddress = (addr: string, length = 6) => {
    if (addr.length <= length * 2) return addr
    return `${addr.slice(0, length + 2)}...${addr.slice(-length)}`
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto py-8 px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center gap-4 mb-4">
              <Link 
                href="/swaps" 
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                ← Back to Search
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Swap Transactions
            </h1>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600 mb-1">Address:</p>
              <p className="font-mono text-lg text-gray-900 break-all">{address}</p>
              <p className="text-sm text-gray-500 mt-1">
                Showing all swap transactions for this address
              </p>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Loading transactions...</span>
            </div>
          )}

          {/* Error State */}
          {isError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <div className="flex">
                <div className="text-red-800">
                  <h3 className="text-sm font-medium">Error loading transactions</h3>
                  <p className="text-sm mt-1">
                    Failed to fetch swap transactions for this address. Please check the address and try again.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !isError && transactions.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">
                <p className="text-lg font-medium">No transactions found</p>
                <p className="text-sm mt-1">
                  No swap transactions found for this address.
                </p>
              </div>
            </div>
          )}

          {/* Results Table */}
          {!isLoading && !isError && transactions.length > 0 && (
            <>
              <div className="mb-4">
                <p className="text-sm text-gray-600">
                  Found {transactions.length} transaction{transactions.length !== 1 ? 's' : ''}
                </p>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Transaction Hash
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Timestamp
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IOTX Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        SOL Amount
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        IOTX/SOL Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount (USD)
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {transactions.map((tx: SwapTransaction, index: number) => (
                      <tr key={`${tx.transaction}-${index}`} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-mono text-gray-900">
                            {truncateHash(tx.transaction)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatTimestamp(tx.timestamp)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm font-medium ${getAmountColor(tx.iotx_amount)}`}>
                            {formatTokenAmount(tx.iotx_amount)} IOTX
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm font-medium ${getAmountColor(tx.sol_amount)}`}>
                            {formatTokenAmount(tx.sol_amount)} SOL
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-blue-600">
                            {formatPrice(tx.iotx_per_sol)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatAmount(tx.amount_usd)}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
