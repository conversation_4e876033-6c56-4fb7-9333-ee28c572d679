import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import SWRProvider from '@/components/SWRProvider'
import Navigation from '@/components/Navigation'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ARB-AIO Frontend',
  description: 'Next.js frontend for ARB-AIO project with SWR',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SWRProvider>
          <Navigation />
          {children}
        </SWRProvider>
      </body>
    </html>
  )
}
