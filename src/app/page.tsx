'use client'

import { useState } from 'react'
import { useApiInfo, useUsers, useCreateUser, useDeleteUser } from '@/hooks/useApi'
import UserStats from '@/components/UserStats'

export default function Home() {
  const [newUserName, setNewUserName] = useState('')
  const [newUserEmail, setNewUserEmail] = useState('')

  // SWR hooks
  const { apiInfo, isLoading: apiLoading, isError: apiError } = useApiInfo()
  const { users, isLoading: usersLoading, isError: usersError, mutate: mutateUsers } = useUsers()
  const { createUser, isCreating, error: createError } = useCreateUser()
  const { deleteUser, isDeleting, error: deleteError } = useDeleteUser()

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newUserName.trim() || !newUserEmail.trim()) return

    try {
      await createUser({
        name: newUserName.trim(),
        email: newUserEmail.trim()
      })

      // Reset form
      setNewUserName('')
      setNewUserEmail('')

      // Revalidate users list
      mutateUsers()
    } catch (err) {
      console.error('Error creating user:', err)
    }
  }

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) return

    try {
      await deleteUser({ id: userId })

      // Revalidate users list
      mutateUsers()
    } catch (err) {
      console.error('Error deleting user:', err)
    }
  }

  const isLoading = apiLoading || usersLoading
  const hasError = apiError || usersError

  if (isLoading) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="text-xl">Loading...</div>
      </main>
    )
  }

  if (hasError) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="text-xl text-red-500">Failed to connect to backend API</div>
        <div className="mt-4 text-sm text-gray-600">
          Make sure the backend server is running on http://localhost:8000
        </div>
        {(createError || deleteError) && (
          <div className="mt-2 text-sm text-red-400">
            {createError?.message || deleteError?.message}
          </div>
        )}
      </main>
    )
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex">
        <p className="fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-6 pt-8 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30">
          ARB-AIO Frontend&nbsp;
          <code className="font-mono font-bold">Next.js + Hono</code>
        </p>
      </div>

      <div className="relative flex place-items-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-8">Welcome to ARB-AIO</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {apiInfo && (
              <div className="p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                <h2 className="text-2xl font-semibold mb-4">Backend API Status (SWR)</h2>
                <p className="mb-2">Message: {apiInfo.message}</p>
                <p className="mb-2">Version: {apiInfo.version}</p>
                <p className="text-sm text-gray-600">Last updated: {apiInfo.timestamp}</p>
              </div>
            )}
            <UserStats />
          </div>

          {/* Create User Form */}
          <div className="mb-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
            <h2 className="text-2xl font-semibold mb-4">Create New User (SWR)</h2>
            <form onSubmit={handleCreateUser} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={newUserName}
                  onChange={(e) => setNewUserName(e.target.value)}
                  className="w-full px-3 py-2 bg-white/20 rounded border border-white/30 text-white placeholder-white/60"
                  placeholder="Enter user name"
                  required
                  disabled={isCreating}
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={newUserEmail}
                  onChange={(e) => setNewUserEmail(e.target.value)}
                  className="w-full px-3 py-2 bg-white/20 rounded border border-white/30 text-white placeholder-white/60"
                  placeholder="Enter user email"
                  required
                  disabled={isCreating}
                />
              </div>
              <button
                type="submit"
                disabled={isCreating || !newUserName.trim() || !newUserEmail.trim()}
                className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-white font-medium transition-colors"
              >
                {isCreating ? 'Creating...' : 'Create User'}
              </button>
              {createError && (
                <div className="text-red-400 text-sm mt-2">
                  Error: {createError.message}
                </div>
              )}
            </form>
          </div>

          {/* Users List */}
          {users.length > 0 && (
            <div className="mb-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
              <h2 className="text-2xl font-semibold mb-4">Users from SWR API</h2>
              <div className="space-y-2">
                {users.map(user => (
                  <div key={user.id} className="p-3 bg-white/20 rounded flex justify-between items-center">
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <p className="text-xs text-gray-500">ID: {user.id}</p>
                    </div>
                    <button
                      onClick={() => handleDeleteUser(user.id)}
                      disabled={isDeleting}
                      className="px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white text-sm rounded transition-colors"
                    >
                      {isDeleting ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                ))}
              </div>
              {deleteError && (
                <div className="text-red-400 text-sm mt-2">
                  Error: {deleteError.message}
                </div>
              )}
            </div>
          )}

          {/* Empty state */}
          {!usersLoading && users.length === 0 && (
            <div className="mb-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
              <h2 className="text-2xl font-semibold mb-4">No Users Found</h2>
              <p className="text-gray-400">Create your first user using the form above.</p>
            </div>
          )}
        </div>
      </div>

      <div className="mb-32 grid text-center lg:max-w-6xl lg:w-full lg:mb-0 lg:grid-cols-5 lg:text-left">
        <a
          href="https://nextjs.org/docs"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Next.js{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Find in-depth information about Next.js features and API.
          </p>
        </a>

        <a
          href="https://swr.vercel.app"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            SWR{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Data fetching library with caching, revalidation, and more.
          </p>
        </a>

        <a
          href="https://hono.dev"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Hono{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Learn about Hono, the fast web framework for the edge.
          </p>
        </a>

        <a
          href="http://localhost:8000"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            API{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Access the backend API directly.
          </p>
        </a>

        <a
          href="/swaps"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Swap Transactions{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Query swap transactions by wallet address.
          </p>
        </a>
      </div>
    </main>
  )
}
