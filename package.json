{"name": "arb-aio", "private": "true", "module": "index.ts", "type": "module", "workspaces": ["packages/backend", "packages/frontend"], "scripts": {"clean": "rm -rf node_modules packages/*/node_modules bun.lockb packages/*/bun.lockb", "dev": "concurrently \"bun run dev:backend\" \"bun run dev:frontend\"", "dev:backend": "cd packages/backend && bun run dev", "dev:frontend": "cd packages/frontend && bun run dev", "build": "bun run build:backend && bun run build:frontend", "build:backend": "cd packages/backend && bun run build", "build:frontend": "cd packages/frontend && bun run build", "start": "concurrently \"bun run start:backend\" \"bun run start:frontend\"", "start:backend": "cd packages/backend && bun run start", "start:frontend": "cd packages/frontend && bun run start", "install:all": "bun install && cd packages/backend && bun install && cd ../frontend && bun install"}, "devDependencies": {"concurrently": "^9.2.0"}}