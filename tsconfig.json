{
  "compilerOptions": {
    "lib": ["ESNext"],
    "module": "nodenext",
    "target": "esnext",
    "moduleResolution": "nodenext",
    "strict": true,
    "downlevelIteration": true,
    "skipLibCheck": true,
    "jsx": "preserve",
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "allowJs": true,
    "types": [
      "bun-types" // add Bun global
    ]
  }
}
