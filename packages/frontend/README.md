# ARB-AIO Frontend

A Next.js frontend application for the ARB-AIO project with SWR data fetching.

## Features

- Built with [Next.js 14](https://nextjs.org/) with App Router
- [SWR](https://swr.vercel.app/) for data fetching with caching and revalidation
- [TypeScript](https://www.typescriptlang.org/) for type safety
- [Tailwind CSS](https://tailwindcss.com/) for styling
- API integration with Hono backend
- Real-time data updates
- Optimistic UI updates
- Automatic error retry
- Responsive design
- ESLint for code quality
- **Swap Transactions Query** - Search and view swap transactions by wallet address

## Getting Started

### Development

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Open http://localhost:3000 in your browser
```

### Build

```bash
# Build for production
bun run build

# Start production server
bun run start
```

### Linting

```bash
# Run ESLint
bun run lint

# Type checking
bun run type-check
```

## Project Structure

```
src/
├── app/                 # App Router pages and layouts
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Home page
├── components/         # Reusable components
└── lib/               # Utility functions
```

## SWR Integration

The frontend uses SWR for all API communications, providing:

- **Automatic Caching**: API responses are cached for better performance
- **Background Revalidation**: Data stays fresh with automatic background updates
- **Error Handling**: Built-in retry logic with exponential backoff
- **Loading States**: Proper loading indicators for all operations
- **Optimistic Updates**: UI updates immediately for better user experience
- **Real-time Statistics**: Live user statistics that update automatically

## Swap Transactions Feature

The application includes a dedicated page for querying swap transactions:

- **Address-based Search**: Enter any wallet address to find related swap transactions
- **Real-time Data**: Fetches live data from the backend database
- **Detailed Transaction Info**: Shows transaction hash, timestamp, token pairs, and USD amounts
- **Responsive Table**: Mobile-friendly transaction display with proper formatting
- **Error Handling**: Graceful error handling with user-friendly messages
- **Loading States**: Clear loading indicators during data fetching

### Usage

1. Navigate to the "Swap Transactions" page using the navigation menu
2. Enter a wallet address in the search field (e.g., `******************************************`)
3. Click "Search" to fetch transactions
4. View the results in a formatted table with transaction details

## API Integration

The frontend communicates with the Hono backend running on `http://localhost:8000` using SWR hooks:

- `useApiInfo()` - Backend status information
- `useUsers()` - Users list with automatic revalidation
- `useCreateUser()` - Create new users with optimistic updates
- `useDeleteUser()` - Delete users with immediate UI feedback
- `useSwapTransactions()` - Query swap transactions by wallet address

## Tech Stack

- [Next.js 14](https://nextjs.org/) - React framework
- [React 18](https://reactjs.org/) - UI library
- [SWR](https://swr.vercel.app/) - Data fetching library
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Bun](https://bun.sh/) - Package manager and runtime
