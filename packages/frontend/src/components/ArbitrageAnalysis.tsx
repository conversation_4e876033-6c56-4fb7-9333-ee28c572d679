'use client'

import { ArbitrageSummary, ArbitrageAnalysis } from '@/lib/api'

interface ArbitrageAnalysisProps {
  summary?: ArbitrageSummary
  analysis?: ArbitrageAnalysis
  showSummary?: boolean
}

export default function ArbitrageAnalysisComponent({ 
  summary, 
  analysis, 
  showSummary = true 
}: ArbitrageAnalysisProps) {
  
  const getOpportunityColor = (opportunity: string) => {
    switch (opportunity) {
      case 'BUY_ON_SWAP':
        return 'text-green-600 bg-green-50'
      case 'BUY_ON_BINANCE':
        return 'text-blue-600 bg-blue-50'
      case 'NO_ARBITRAGE':
        return 'text-gray-600 bg-gray-50'
      default:
        return 'text-yellow-600 bg-yellow-50'
    }
  }

  const getOpportunityText = (opportunity: string) => {
    switch (opportunity) {
      case 'BUY_ON_SWAP':
        return 'Buy on Swap'
      case 'BUY_ON_BINANCE':
        return 'Buy on Binance'
      case 'NO_ARBITRAGE':
        return 'No Arbitrage'
      default:
        return 'Unknown'
    }
  }

  const formatPercentage = (value: number | null) => {
    if (value === null || !isFinite(value)) return 'N/A'
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`
  }

  const formatPrice = (price: number | null, decimals = 2) => {
    if (price === null || !isFinite(price)) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(price)
  }

  return (
    <div className="space-y-4">
      {/* Summary Section */}
      {showSummary && summary && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Arbitrage Analysis Summary
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {summary.total_transactions}
              </div>
              <div className="text-sm text-gray-500">Total Transactions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {summary.analyzed_transactions}
              </div>
              <div className="text-sm text-gray-500">Analyzed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {summary.arbitrage_opportunities}
              </div>
              <div className="text-sm text-gray-500">Opportunities</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {summary.profitable_transactions}
              </div>
              <div className="text-sm text-gray-500">Profitable (>1%)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatPercentage(summary.average_price_difference)}
              </div>
              <div className="text-sm text-gray-500">Avg Price Diff</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">
                {formatPercentage(summary.max_profit_opportunity)}
              </div>
              <div className="text-sm text-gray-500">Max Profit</div>
            </div>
          </div>
        </div>
      )}

      {/* Individual Analysis Section */}
      {analysis && (
        <div className="bg-white rounded-lg shadow p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div className="text-sm text-gray-500">Swap Price</div>
              <div className="text-lg font-semibold text-gray-900">
                {formatPrice(analysis.swap_iotx_per_sol)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Binance Price</div>
              <div className="text-lg font-semibold text-gray-900">
                {formatPrice(analysis.binance_iotx_per_sol)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Price Difference</div>
              <div className={`text-lg font-semibold ${
                analysis.price_difference_percentage && analysis.price_difference_percentage > 0 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {formatPercentage(analysis.price_difference_percentage)}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Opportunity</div>
              <div className={`inline-flex px-2 py-1 rounded-full text-sm font-medium ${
                getOpportunityColor(analysis.arbitrage_opportunity)
              }`}>
                {getOpportunityText(analysis.arbitrage_opportunity)}
              </div>
            </div>
          </div>
          
          {analysis.potential_profit_percentage && analysis.potential_profit_percentage > 0 && (
            <div className="mt-4 p-3 bg-green-50 rounded-lg">
              <div className="text-sm text-green-800">
                <strong>Potential Profit: {formatPercentage(analysis.potential_profit_percentage)}</strong>
              </div>
              <div className="text-xs text-green-600 mt-1">
                {analysis.arbitrage_opportunity === 'BUY_ON_BINANCE' 
                  ? 'Strategy: Buy IOTX/SOL on Binance, sell on the swap platform'
                  : 'Strategy: Buy IOTX/SOL on the swap platform, sell on Binance'
                }
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
