// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

// Generic fetcher function for SWR
export const fetcher = async (url: string) => {
  const response = await fetch(url)
  
  if (!response.ok) {
    const error = new Error('An error occurred while fetching the data.')
    // Attach extra info to the error object
    ;(error as any).info = await response.json()
    ;(error as any).status = response.status
    throw error
  }
  
  return response.json()
}

// API endpoints
export const API_ENDPOINTS = {
  apiInfo: `${API_BASE_URL}/`,
  health: `${API_BASE_URL}/health`,
  users: `${API_BASE_URL}/api/users`,
  user: (id: number) => `${API_BASE_URL}/api/users/${id}`,
  swapTxs: (address: string, withArbitrage: boolean = false) =>
    `${API_BASE_URL}/${address}/txs${withArbitrage ? '?arbitrage=true' : ''}`,
} as const

// API functions for mutations
export const apiMutations = {
  // Create user
  async createUser(userData: { name: string; email: string }) {
    const response = await fetch(API_ENDPOINTS.users, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to create user')
    }

    return response.json()
  },

  // Update user
  async updateUser(id: number, userData: { name: string; email: string }) {
    const response = await fetch(API_ENDPOINTS.user(id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to update user')
    }

    return response.json()
  },

  // Delete user
  async deleteUser(id: number) {
    const response = await fetch(API_ENDPOINTS.user(id), {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to delete user')
    }

    return response.json()
  },
}

// Types
export interface User {
  id: number
  name: string
  email: string
}

export interface ApiResponse {
  message?: string
  version?: string
  timestamp?: string
}

export interface UsersResponse {
  users: User[]
}

export interface UserResponse {
  user: User
}

export interface CreateUserResponse {
  message: string
  user: User
}

// Arbitrage analysis types
export interface ArbitrageAnalysis {
  binance_iotx_per_sol: number | null
  swap_iotx_per_sol: number
  price_difference: number | null
  price_difference_percentage: number | null
  arbitrage_opportunity: 'BUY_ON_SWAP' | 'BUY_ON_BINANCE' | 'NO_ARBITRAGE' | 'UNKNOWN'
  potential_profit_percentage: number | null
}

export interface ArbitrageSummary {
  total_transactions: number
  analyzed_transactions: number
  arbitrage_opportunities: number
  average_price_difference: number
  max_profit_opportunity: number
  profitable_transactions: number
}

// Swap transaction types
export interface SwapTransaction {
  transaction: string
  timestamp: string
  iotx_amount: string
  sol_amount: string
  amount_usd: number
  iotx_per_sol: number
  arbitrage?: ArbitrageAnalysis
}

export interface SwapTransactionsResponse {
  list: SwapTransaction[]
  arbitrage_summary?: ArbitrageSummary
  error?: string
}
