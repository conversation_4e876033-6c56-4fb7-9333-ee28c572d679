import { BinanceService } from './binanceService'
import { PriceDataService } from './priceDataService'

export interface ArbitrageAnalysis {
  binance_iotx_per_sol: number | null
  swap_iotx_per_sol: number
  price_difference: number | null
  price_difference_percentage: number | null
  arbitrage_opportunity: 'BUY_ON_SWAP' | 'BUY_ON_BINANCE' | 'NO_ARBITRAGE' | 'UNKNOWN'
  potential_profit_percentage: number | null
}

export interface SwapTransactionWithArbitrage {
  transaction: string
  timestamp: string
  iotx_amount: string
  sol_amount: string
  amount_usd: number
  iotx_per_sol: number
  arbitrage: ArbitrageAnalysis
}

export class ArbitrageService {
  /**
   * Analyze arbitrage opportunity for a single swap transaction
   */
  static async analyzeTransaction(
    transaction: any,
    enableBinanceCheck: boolean = true
  ): Promise<SwapTransactionWithArbitrage> {
    const swapPrice = transaction.iotx_per_sol
    const timestamp = parseInt(transaction.timestamp)
    
    let arbitrage: ArbitrageAnalysis = {
      binance_iotx_per_sol: null,
      swap_iotx_per_sol: swapPrice,
      price_difference: null,
      price_difference_percentage: null,
      arbitrage_opportunity: 'UNKNOWN',
      potential_profit_percentage: null
    }
    
    if (enableBinanceCheck && swapPrice && isFinite(swapPrice)) {
      try {
        // Try to get prices from cache first, then fallback to API
        const [iotxPrice, solPrice] = await Promise.all([
          PriceDataService.getPrice('IOTXUSDT', timestamp, true),
          PriceDataService.getPrice('SOLUSDT', timestamp, true)
        ])

        const binancePrice = (iotxPrice && solPrice && solPrice.price > 0)
          ? solPrice.price / iotxPrice.price
          : null
        
        if (binancePrice && isFinite(binancePrice)) {
          arbitrage.binance_iotx_per_sol = binancePrice
          arbitrage.price_difference = swapPrice - binancePrice
          arbitrage.price_difference_percentage = ((swapPrice - binancePrice) / binancePrice) * 100
          
          // Determine arbitrage opportunity
          const threshold = 0.5 // 0.5% threshold for considering arbitrage
          
          if (Math.abs(arbitrage.price_difference_percentage) < threshold) {
            arbitrage.arbitrage_opportunity = 'NO_ARBITRAGE'
            arbitrage.potential_profit_percentage = 0
          } else if (arbitrage.price_difference_percentage > threshold) {
            // Swap price is higher than Binance - buy on Binance, sell on swap
            arbitrage.arbitrage_opportunity = 'BUY_ON_BINANCE'
            arbitrage.potential_profit_percentage = arbitrage.price_difference_percentage
          } else {
            // Swap price is lower than Binance - buy on swap, sell on Binance
            arbitrage.arbitrage_opportunity = 'BUY_ON_SWAP'
            arbitrage.potential_profit_percentage = Math.abs(arbitrage.price_difference_percentage)
          }
        }
      } catch (error) {
        console.error('Error analyzing arbitrage for transaction:', transaction.transaction, error)
      }
    }
    
    return {
      ...transaction,
      arbitrage
    }
  }
  
  /**
   * Pre-download prices for specific timestamps
   */
  static async preDownloadPricesForTimestamps(timestamps: number[]): Promise<void> {
    console.log(`Pre-downloading prices for ${timestamps.length} timestamps...`)

    // Get unique timestamps and sort them
    const uniqueTimestamps = [...new Set(timestamps)].sort((a, b) => a - b)

    for (const timestamp of uniqueTimestamps) {
      try {
        // Try to get prices, which will cache them if successful
        await Promise.all([
          PriceDataService.getPrice('IOTXUSDT', timestamp, false), // force download
          PriceDataService.getPrice('SOLUSDT', timestamp, false)   // force download
        ])

        console.log(`Downloaded prices for timestamp ${timestamp} (${new Date(timestamp * 1000).toISOString()})`)

        // Rate limiting - 200ms between requests
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (error) {
        console.error(`Error downloading prices for timestamp ${timestamp}:`, error)
      }
    }

    console.log('Pre-download completed')
  }

  /**
   * Analyze arbitrage opportunities for multiple transactions
   * Uses batch processing and pre-downloads prices for better performance
   */
  static async analyzeTransactions(
    transactions: any[],
    enableBinanceCheck: boolean = true,
    batchSize: number = 5
  ): Promise<SwapTransactionWithArbitrage[]> {
    const results: SwapTransactionWithArbitrage[] = []

    if (!enableBinanceCheck) {
      // Return transactions without arbitrage analysis
      return transactions.map(tx => ({
        ...tx,
        arbitrage: {
          binance_iotx_per_sol: null,
          swap_iotx_per_sol: tx.iotx_per_sol,
          price_difference: null,
          price_difference_percentage: null,
          arbitrage_opportunity: 'UNKNOWN',
          potential_profit_percentage: null
        }
      }))
    }

    // Pre-download prices for all timestamps
    const timestamps = transactions
      .map(tx => parseInt(tx.timestamp))
      .filter(ts => !isNaN(ts))

    if (timestamps.length > 0) {
      await this.preDownloadPricesForTimestamps(timestamps)
    }

    // Process transactions in batches (faster now since prices are cached)
    for (let i = 0; i < transactions.length; i += batchSize) {
      const batch = transactions.slice(i, i + batchSize)

      const batchPromises = batch.map(tx =>
        this.analyzeTransaction(tx, enableBinanceCheck)
      )

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Minimal delay since prices are pre-cached
      if (i + batchSize < transactions.length) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }

    return results
  }
  
  /**
   * Get summary statistics for arbitrage analysis
   */
  static getArbitrageSummary(transactions: SwapTransactionWithArbitrage[]) {
    const validArbitrages = transactions.filter(tx => 
      tx.arbitrage.binance_iotx_per_sol !== null &&
      tx.arbitrage.arbitrage_opportunity !== 'UNKNOWN'
    )
    
    if (validArbitrages.length === 0) {
      return {
        total_transactions: transactions.length,
        analyzed_transactions: 0,
        arbitrage_opportunities: 0,
        average_price_difference: 0,
        max_profit_opportunity: 0,
        profitable_transactions: 0
      }
    }
    
    const arbitrageOpportunities = validArbitrages.filter(tx => 
      tx.arbitrage.arbitrage_opportunity !== 'NO_ARBITRAGE'
    )
    
    const profitableTransactions = arbitrageOpportunities.filter(tx =>
      tx.arbitrage.potential_profit_percentage && tx.arbitrage.potential_profit_percentage > 1
    )
    
    const avgPriceDiff = validArbitrages.reduce((sum, tx) => 
      sum + (tx.arbitrage.price_difference_percentage || 0), 0
    ) / validArbitrages.length
    
    const maxProfit = Math.max(...arbitrageOpportunities.map(tx => 
      tx.arbitrage.potential_profit_percentage || 0
    ))
    
    return {
      total_transactions: transactions.length,
      analyzed_transactions: validArbitrages.length,
      arbitrage_opportunities: arbitrageOpportunities.length,
      average_price_difference: avgPriceDiff,
      max_profit_opportunity: maxProfit,
      profitable_transactions: profitableTransactions.length
    }
  }
}
