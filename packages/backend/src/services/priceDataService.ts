import { promises as fs } from 'fs'
import path from 'path'
import { BinanceService, type PriceData } from './binanceService'

export interface CachedPriceData {
  symbol: string
  timestamp: number
  price: number
  cached_at: number
}

export interface PriceDataCache {
  [symbol: string]: {
    [timestamp: string]: CachedPriceData
  }
}

export class PriceDataService {
  private static readonly CACHE_DIR = path.join(process.cwd(), 'data', 'price_cache')
  private static readonly CACHE_FILE = path.join(this.CACHE_DIR, 'binance_prices.json')
  private static cache: PriceDataCache = {}
  private static cacheLoaded = false

  /**
   * Ensure cache directory exists
   */
  private static async ensureCacheDir(): Promise<void> {
    try {
      await fs.mkdir(this.CACHE_DIR, { recursive: true })
    } catch (error) {
      console.error('Error creating cache directory:', error)
    }
  }

  /**
   * Load cache from file
   */
  private static async loadCache(): Promise<void> {
    if (this.cacheLoaded) return

    try {
      await this.ensureCacheDir()
      const data = await fs.readFile(this.CACHE_FILE, 'utf-8')
      this.cache = JSON.parse(data)
      console.log('Price cache loaded successfully')
    } catch (error) {
      console.log('No existing price cache found, starting with empty cache')
      this.cache = {}
    }
    this.cacheLoaded = true
  }

  /**
   * Save cache to file
   */
  private static async saveCache(): Promise<void> {
    try {
      await this.ensureCacheDir()
      await fs.writeFile(this.CACHE_FILE, JSON.stringify(this.cache, null, 2))
      console.log('Price cache saved successfully')
    } catch (error) {
      console.error('Error saving price cache:', error)
    }
  }

  /**
   * Get cached price data
   */
  static async getCachedPrice(symbol: string, timestamp: number): Promise<CachedPriceData | null> {
    await this.loadCache()
    
    const symbolCache = this.cache[symbol]
    if (!symbolCache) return null

    const timestampKey = timestamp.toString()
    return symbolCache[timestampKey] || null
  }

  /**
   * Cache price data
   */
  static async cachePrice(symbol: string, timestamp: number, price: number): Promise<void> {
    await this.loadCache()

    if (!this.cache[symbol]) {
      this.cache[symbol] = {}
    }

    const timestampKey = timestamp.toString()
    this.cache[symbol][timestampKey] = {
      symbol,
      timestamp,
      price,
      cached_at: Math.floor(Date.now() / 1000)
    }

    await this.saveCache()
  }

  /**
   * Get price with cache fallback
   */
  static async getPrice(symbol: string, timestamp: number, useCache: boolean = true): Promise<PriceData | null> {
    // Try cache first if enabled
    if (useCache) {
      const cached = await this.getCachedPrice(symbol, timestamp)
      if (cached) {
        console.log(`Using cached price for ${symbol} at ${timestamp}`)
        return {
          timestamp: cached.timestamp,
          price: cached.price
        }
      }
    }

    // Fetch from Binance API
    console.log(`Fetching price for ${symbol} at ${timestamp} from Binance API`)
    const priceData = await BinanceService.getHistoricalPrice(symbol, timestamp)
    
    if (priceData) {
      // Cache the result
      await this.cachePrice(symbol, timestamp, priceData.price)
    }

    return priceData
  }

  /**
   * Download price data for a date range
   */
  static async downloadPriceRange(
    symbol: string, 
    startTimestamp: number, 
    endTimestamp: number, 
    intervalMinutes: number = 60
  ): Promise<number> {
    await this.loadCache()
    
    const intervalSeconds = intervalMinutes * 60
    let downloadedCount = 0
    let errorCount = 0
    
    console.log(`Downloading ${symbol} price data from ${new Date(startTimestamp * 1000)} to ${new Date(endTimestamp * 1000)}`)
    
    for (let timestamp = startTimestamp; timestamp <= endTimestamp; timestamp += intervalSeconds) {
      try {
        // Check if already cached
        const cached = await this.getCachedPrice(symbol, timestamp)
        if (cached) {
          console.log(`Skipping ${symbol} at ${timestamp} (already cached)`)
          continue
        }

        // Fetch from API
        const priceData = await BinanceService.getHistoricalPrice(symbol, timestamp)
        if (priceData) {
          await this.cachePrice(symbol, timestamp, priceData.price)
          downloadedCount++
          console.log(`Downloaded ${symbol} price at ${new Date(timestamp * 1000)}: $${priceData.price}`)
        } else {
          errorCount++
          console.warn(`Failed to get price for ${symbol} at ${timestamp}`)
        }

        // Rate limiting - wait between requests
        await new Promise(resolve => setTimeout(resolve, 200))
        
      } catch (error) {
        errorCount++
        console.error(`Error downloading ${symbol} price at ${timestamp}:`, error)
        
        // Wait longer on error
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    console.log(`Download complete for ${symbol}: ${downloadedCount} prices downloaded, ${errorCount} errors`)
    return downloadedCount
  }

  /**
   * Download recent price data (last N days)
   */
  static async downloadRecentPrices(symbol: string, days: number = 7, intervalMinutes: number = 60): Promise<number> {
    const endTimestamp = Math.floor(Date.now() / 1000)
    const startTimestamp = endTimestamp - (days * 24 * 60 * 60)
    
    return this.downloadPriceRange(symbol, startTimestamp, endTimestamp, intervalMinutes)
  }

  /**
   * Get cache statistics
   */
  static async getCacheStats(): Promise<{ [symbol: string]: number }> {
    await this.loadCache()
    
    const stats: { [symbol: string]: number } = {}
    for (const symbol in this.cache) {
      stats[symbol] = Object.keys(this.cache[symbol]).length
    }
    
    return stats
  }

  /**
   * Clear cache for a symbol or all symbols
   */
  static async clearCache(symbol?: string): Promise<void> {
    await this.loadCache()
    
    if (symbol) {
      delete this.cache[symbol]
      console.log(`Cache cleared for ${symbol}`)
    } else {
      this.cache = {}
      console.log('All cache cleared')
    }
    
    await this.saveCache()
  }
}
