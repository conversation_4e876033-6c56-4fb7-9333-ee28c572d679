// Binance API service for fetching historical price data
// Binance klines API returns arrays in this format:
// [openTime, open, high, low, close, volume, closeTime, quoteAssetVolume, numberOfTrades, takerBuyBaseAssetVolume, takerBuyQuoteAssetVolume, ignore]
export type BinanceKlineData = [
  number, // openTime
  string, // open
  string, // high
  string, // low
  string, // close
  string, // volume
  number, // closeTime
  string, // quoteAssetVolume
  number, // numberOfTrades
  string, // takerBuyBaseAssetVolume
  string, // takerBuyQuoteAssetVolume
  string  // ignore
]

export interface PriceData {
  timestamp: number
  price: number
}

export class BinanceService {
  private static readonly BASE_URL = 'https://api.binance.com/api/v3'
  
  /**
   * Get historical price data for a symbol at a specific timestamp
   * @param symbol Trading pair symbol (e.g., 'IOTXUSDT', 'SOLUSDT')
   * @param timestamp Unix timestamp in seconds
   * @returns Price data or null if not found
   */
  static async getHistoricalPrice(symbol: string, timestamp: number): Promise<PriceData | null> {
    try {
      // Convert timestamp to milliseconds for Binance API
      const timestampMs = timestamp * 1000
      
      // Get kline data for the specific time period
      // Using 5m interval to get good coverage around the timestamp
      const startTime = timestampMs - (5 * 60 * 1000) // 5 minutes before
      const endTime = timestampMs + (5 * 60 * 1000)   // 5 minutes after

      const url = `${this.BASE_URL}/klines?symbol=${symbol}&interval=5m&startTime=${startTime}&endTime=${endTime}&limit=5`
      
      const response = await fetch(url)
      if (!response.ok) {
        console.error(`Binance API error for ${symbol}:`, response.status, response.statusText)
        return null
      }
      
      const klines: BinanceKlineData[] = await response.json()

      if (klines.length === 0) {
        console.warn(`No price data found for ${symbol} at timestamp ${timestamp}`)
        return null
      }

      // Find the closest kline to our target timestamp
      let closestKline = klines[0]
      let minTimeDiff = Math.abs(closestKline[0] - timestampMs) // openTime is at index 0

      for (const kline of klines) {
        const timeDiff = Math.abs(kline[0] - timestampMs) // openTime is at index 0
        if (timeDiff < minTimeDiff) {
          minTimeDiff = timeDiff
          closestKline = kline
        }
      }

      // Use the close price as the reference price
      return {
        timestamp: Math.floor(closestKline[0] / 1000), // openTime is at index 0
        price: parseFloat(closestKline[4]) // close price is at index 4
      }
    } catch (error) {
      console.error(`Error fetching price for ${symbol}:`, error)
      return null
    }
  }
  
  /**
   * Get IOTX price in USDT at a specific timestamp
   */
  static async getIOTXPrice(timestamp: number): Promise<PriceData | null> {
    return this.getHistoricalPrice('IOTXUSDT', timestamp)
  }
  
  /**
   * Get SOL price in USDT at a specific timestamp
   */
  static async getSOLPrice(timestamp: number): Promise<PriceData | null> {
    return this.getHistoricalPrice('SOLUSDT', timestamp)
  }
  
  /**
   * Calculate IOTX per SOL ratio from Binance prices
   * @param timestamp Unix timestamp in seconds
   * @returns IOTX per SOL ratio or null if prices not available
   */
  static async getIOTXPerSOLFromBinance(timestamp: number): Promise<number | null> {
    try {
      const [iotxPrice, solPrice] = await Promise.all([
        this.getIOTXPrice(timestamp),
        this.getSOLPrice(timestamp)
      ])
      
      if (!iotxPrice || !solPrice || solPrice.price === 0) {
        return null
      }
      
      // Calculate IOTX per SOL: SOL_price / IOTX_price
      return solPrice.price / iotxPrice.price
    } catch (error) {
      console.error('Error calculating IOTX per SOL from Binance:', error)
      return null
    }
  }
  
  /**
   * Add rate limiting to avoid hitting Binance API limits
   */
  private static lastRequestTime = 0
  private static readonly MIN_REQUEST_INTERVAL = 100 // 100ms between requests
  
  private static async rateLimit(): Promise<void> {
    const now = Date.now()
    const timeSinceLastRequest = now - this.lastRequestTime
    
    if (timeSinceLastRequest < this.MIN_REQUEST_INTERVAL) {
      const waitTime = this.MIN_REQUEST_INTERVAL - timeSinceLastRequest
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
    
    this.lastRequestTime = Date.now()
  }
}
