#!/usr/bin/env bun

import { PriceDataService } from '../services/priceDataService'

interface CliOptions {
  symbol?: string
  days?: number
  interval?: number
  start?: string
  end?: string
  stats?: boolean
  clear?: boolean
  help?: boolean
}

function parseArgs(): CliOptions {
  const args = process.argv.slice(2)
  const options: CliOptions = {}

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--symbol':
      case '-s':
        options.symbol = args[++i]
        break
      case '--days':
      case '-d':
        options.days = parseInt(args[++i])
        break
      case '--interval':
      case '-i':
        options.interval = parseInt(args[++i])
        break
      case '--start':
        options.start = args[++i]
        break
      case '--end':
        options.end = args[++i]
        break
      case '--stats':
        options.stats = true
        break
      case '--clear':
        options.clear = true
        break
      case '--help':
      case '-h':
        options.help = true
        break
    }
  }

  return options
}

function showHelp() {
  console.log(`
Binance Price Data Downloader

Usage:
  bun run cli/downloadPrices.ts [options]

Options:
  -s, --symbol <symbol>     Token symbol (e.g., IOTXUSDT, SOLUSDT)
  -d, --days <days>         Number of days to download (default: 7)
  -i, --interval <minutes>  Interval between data points in minutes (default: 60)
  --start <date>            Start date (YYYY-MM-DD format)
  --end <date>              End date (YYYY-MM-DD format)
  --stats                   Show cache statistics
  --clear                   Clear cache for symbol (or all if no symbol specified)
  -h, --help                Show this help message

Examples:
  # Download last 7 days of IOTX prices (hourly)
  bun run cli/downloadPrices.ts --symbol IOTXUSDT --days 7

  # Download last 3 days of SOL prices (every 30 minutes)
  bun run cli/downloadPrices.ts --symbol SOLUSDT --days 3 --interval 30

  # Download specific date range
  bun run cli/downloadPrices.ts --symbol IOTXUSDT --start 2024-01-01 --end 2024-01-07

  # Download both IOTX and SOL for last week
  bun run cli/downloadPrices.ts --symbol IOTXUSDT --days 7
  bun run cli/downloadPrices.ts --symbol SOLUSDT --days 7

  # Show cache statistics
  bun run cli/downloadPrices.ts --stats

  # Clear cache for IOTX
  bun run cli/downloadPrices.ts --symbol IOTXUSDT --clear

  # Clear all cache
  bun run cli/downloadPrices.ts --clear
`)
}

function parseDate(dateStr: string): number {
  const date = new Date(dateStr + 'T00:00:00Z')
  if (isNaN(date.getTime())) {
    throw new Error(`Invalid date format: ${dateStr}. Use YYYY-MM-DD format.`)
  }
  return Math.floor(date.getTime() / 1000)
}

async function main() {
  const options = parseArgs()

  if (options.help) {
    showHelp()
    return
  }

  try {
    // Show cache statistics
    if (options.stats) {
      console.log('Cache Statistics:')
      const stats = await PriceDataService.getCacheStats()
      
      if (Object.keys(stats).length === 0) {
        console.log('  No cached data found')
      } else {
        for (const [symbol, count] of Object.entries(stats)) {
          console.log(`  ${symbol}: ${count} data points`)
        }
      }
      return
    }

    // Clear cache
    if (options.clear) {
      await PriceDataService.clearCache(options.symbol)
      return
    }

    // Download price data
    if (!options.symbol) {
      console.error('Error: Symbol is required for downloading. Use --symbol or -s')
      console.log('Use --help for usage information')
      process.exit(1)
    }

    const symbol = options.symbol.toUpperCase()
    
    // Validate symbol format
    if (!symbol.endsWith('USDT')) {
      console.warn(`Warning: Symbol ${symbol} doesn't end with USDT. Make sure this is correct.`)
    }

    let downloadedCount: number

    if (options.start && options.end) {
      // Download specific date range
      const startTimestamp = parseDate(options.start)
      const endTimestamp = parseDate(options.end)
      const intervalMinutes = options.interval || 60

      if (startTimestamp >= endTimestamp) {
        console.error('Error: Start date must be before end date')
        process.exit(1)
      }

      console.log(`Downloading ${symbol} from ${options.start} to ${options.end} (${intervalMinutes}min intervals)`)
      downloadedCount = await PriceDataService.downloadPriceRange(
        symbol, 
        startTimestamp, 
        endTimestamp, 
        intervalMinutes
      )
    } else {
      // Download recent days
      const days = options.days || 7
      const intervalMinutes = options.interval || 60

      console.log(`Downloading last ${days} days of ${symbol} (${intervalMinutes}min intervals)`)
      downloadedCount = await PriceDataService.downloadRecentPrices(symbol, days, intervalMinutes)
    }

    console.log(`\n✅ Download completed: ${downloadedCount} new data points added to cache`)
    
    // Show updated stats
    const stats = await PriceDataService.getCacheStats()
    console.log(`📊 Total cached data points for ${symbol}: ${stats[symbol] || 0}`)

  } catch (error) {
    console.error('❌ Error:', error instanceof Error ? error.message : error)
    process.exit(1)
  }
}

// Run the CLI
main().catch(console.error)
