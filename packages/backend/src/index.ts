import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { db } from './libs/db'
import { ArbitrageService } from './services/arbitrageService'

const app = new Hono()

// Middleware
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', cors({
  origin: ['http://localhost:3000'], // Next.js frontend URL
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
}))

// Routes
app.get('/', (c) => {
  return c.json({ 
    message: 'Welcome to ARB-AIO Backend API',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  })
})

app.get('/health', (c) => {
  return c.json({ 
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  })
})

// API routes
const api = new Hono()

api.get('/users', (c) => {
  return c.json({
    users: [
      { id: 1, name: '<PERSON> Doe', email: '<EMAIL>' },
      { id: 2, name: 'Jane <PERSON>', email: '<EMAIL>' }
    ]
  })
})

api.post('/users', async (c) => {
  const body = await c.req.json()
  return c.json({
    message: 'User created successfully',
    user: { id: Date.now(), ...body }
  }, 201)
})

app.get(':address/txs',  async (c) => {
  const address = c.req.param('address');
  const enableArbitrage = c.req.query('arbitrage') === 'true';

  /**
   * ****************************************** -> WIOTX
   * 0xa1f3f211d9b33f2086a800842836d67f139b9a7a -> WSOL
   */

  // Get current timestamp and 2 days ago timestamp
  const currentTimestamp = Math.floor(Date.now() / 1000)
  const twoDaysAgo = currentTimestamp - (2 * 24 * 60 * 60) // 2 days in seconds

  const swaps = await db`SELECT
    transaction,
    timestamp,
    amount_0 AS iotx_amount,
    amount_1 AS sol_amount,
    amount_usd,
    -- 计算 IOTX 相对于 SOL 的价格
    ABS(amount_0) / NULLIF(ABS(amount_1), 0) AS iotx_per_sol
FROM sgd52.swap
WHERE pool = '0xb1c8d02fa83e8ad39c50bba94e3e63299e13992b'
  AND lower(concat('0x', encode(origin, 'hex'))) = lower(${address})
  AND timestamp >= ${twoDaysAgo}
ORDER BY timestamp DESC;`

  // If arbitrage analysis is requested, analyze the transactions
  if (enableArbitrage) {
    try {
      // Limit to first 10 transactions to avoid timeout
      const limitedSwaps = swaps.slice(0, 10)
      const analyzedSwaps = await ArbitrageService.analyzeTransactions(limitedSwaps, true, 2)
      const summary = ArbitrageService.getArbitrageSummary(analyzedSwaps)

      return c.json({
        list: analyzedSwaps,
        arbitrage_summary: summary,
        note: swaps.length > 10 ? `Showing arbitrage analysis for first 10 of ${swaps.length} transactions` : undefined
      })
    } catch (error) {
      console.error('Error performing arbitrage analysis:', error)
      // Fall back to returning transactions without arbitrage data
      return c.json({
        list: swaps,
        error: 'Arbitrage analysis failed'
      })
    }
  }

  return c.json({
    list: swaps
  })
})

app.route('/api', api)

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error(`${err}`)
  return c.json({ error: 'Internal Server Error' }, 500)
})

const port = process.env.PORT || 8000

console.log(`🚀 Server is running on http://localhost:${port}`)

export default {
  port,
  fetch: app.fetch,
  // Increase timeout for arbitrage analysis
  idleTimeout: 60, // 60 seconds
} as {
  port: string | number
  fetch: (request: Request, env?: any, executionContext?: any) => Response | Promise<Response>
  idleTimeout: number
}
