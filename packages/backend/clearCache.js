// Clear price cache
import { PriceDataService } from './src/services/priceDataService.ts'

async function clearCache() {
  console.log('Clearing price cache...')
  
  try {
    await PriceDataService.clearCache()
    console.log('✅ Cache cleared successfully!')
    
    // Show stats
    const stats = await PriceDataService.getCacheStats()
    console.log('📊 Cache Statistics:', stats)
    
  } catch (error) {
    console.error('❌ Error clearing cache:', error)
    process.exit(1)
  }
}

clearCache()
