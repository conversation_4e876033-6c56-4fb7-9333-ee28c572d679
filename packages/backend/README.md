# ARB-AIO Backend

A Hono-based backend API for the ARB-AIO project.

## Features

- Built with [<PERSON><PERSON>](https://hono.dev/) - Fast, lightweight web framework
- TypeScript support
- CORS enabled for frontend integration
- JSON logging and pretty printing
- Health check endpoint
- RESTful API structure

## Getting Started

### Development

```bash
# Install dependencies
bun install

# Start development server with hot reload
bun run dev

# Start production server
bun run start
```

### Build

```bash
bun run build
```

## API Endpoints

- `GET /` - Welcome message
- `GET /health` - Health check
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user

## Environment Variables

- `PORT` - Server port (default: 8000)

## Tech Stack

- [Bun](https://bun.sh/) - JavaScript runtime and package manager
- [Hono](https://hono.dev/) - Web framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety
