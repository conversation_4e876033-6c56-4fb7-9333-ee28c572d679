// Simple script to download IOTX and SOL price data
import { PriceDataService } from './src/services/priceDataService.ts'

async function downloadPrices() {
  console.log('Starting price data download...')
  
  try {
    // Download IOTX prices for last 7 days (hourly)
    console.log('\n📈 Downloading IOTX prices...')
    const iotxCount = await PriceDataService.downloadRecentPrices('IOTXUSDT', 7, 60)
    console.log(`✅ Downloaded ${iotxCount} IOTX price points`)
    
    // Download SOL prices for last 7 days (hourly)
    console.log('\n📈 Downloading SOL prices...')
    const solCount = await PriceDataService.downloadRecentPrices('SOLUSDT', 7, 60)
    console.log(`✅ Downloaded ${solCount} SOL price points`)
    
    // Show cache stats
    console.log('\n📊 Cache Statistics:')
    const stats = await PriceDataService.getCacheStats()
    for (const [symbol, count] of Object.entries(stats)) {
      console.log(`  ${symbol}: ${count} data points`)
    }
    
    console.log('\n🎉 Price download completed successfully!')
    
  } catch (error) {
    console.error('❌ Error downloading prices:', error)
    process.exit(1)
  }
}

downloadPrices()
