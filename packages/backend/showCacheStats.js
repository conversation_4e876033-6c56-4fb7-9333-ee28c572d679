// Show cache statistics
import { PriceDataService } from './src/services/priceDataService.ts'

async function showCacheStats() {
  console.log('📊 Current Cache Statistics:')
  
  try {
    const stats = await PriceDataService.getCacheStats()
    
    if (Object.keys(stats).length === 0) {
      console.log('  No cached data found')
    } else {
      for (const [symbol, count] of Object.entries(stats)) {
        console.log(`  ${symbol}: ${count} data points`)
      }
    }
    
  } catch (error) {
    console.error('❌ Error getting cache stats:', error)
    process.exit(1)
  }
}

showCacheStats()
