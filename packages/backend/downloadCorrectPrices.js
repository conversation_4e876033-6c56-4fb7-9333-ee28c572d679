// Clear cache and download correct price data
import { PriceDataService } from './src/services/priceDataService.ts'

async function downloadCorrectPrices() {
  console.log('Clearing cache and downloading correct price data...')
  
  try {
    // Clear existing cache
    console.log('🗑️ Clearing existing cache...')
    await PriceDataService.clearCache()
    
    // Download IOTX prices for last 2 days (every 30 minutes)
    console.log('\n📈 Downloading IOTX prices (last 2 days)...')
    const iotxCount = await PriceDataService.downloadRecentPrices('IOTXUSDT', 2, 30)
    console.log(`✅ Downloaded ${iotxCount} IOTX price points`)
    
    // Download SOL prices for last 2 days (every 30 minutes)
    console.log('\n📈 Downloading SOL prices (last 2 days)...')
    const solCount = await PriceDataService.downloadRecentPrices('SOLUSDT', 2, 30)
    console.log(`✅ Downloaded ${solCount} SOL price points`)
    
    // Show cache stats
    console.log('\n📊 Cache Statistics:')
    const stats = await PriceDataService.getCacheStats()
    for (const [symbol, count] of Object.entries(stats)) {
      console.log(`  ${symbol}: ${count} data points`)
    }
    
    console.log('\n🎉 Correct price download completed successfully!')
    
  } catch (error) {
    console.error('❌ Error downloading prices:', error)
    process.exit(1)
  }
}

downloadCorrectPrices()
