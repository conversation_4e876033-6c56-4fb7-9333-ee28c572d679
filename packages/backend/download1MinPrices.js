// Download 1-minute interval price data for better accuracy
import { PriceDataService } from './src/services/priceDataService.ts'

async function download1MinPrices() {
  console.log('Downloading 1-minute interval price data for better accuracy...')
  
  try {
    // Clear existing cache to start fresh
    console.log('🗑️ Clearing existing cache...')
    await PriceDataService.clearCache()
    
    // Download IOTX prices for last 2 days (every 1 minute)
    console.log('\n📈 Downloading IOTX prices (1-minute intervals, last 2 days)...')
    const iotxCount = await PriceDataService.downloadRecentPrices('IOTXUSDT', 2, 1)
    console.log(`✅ Downloaded ${iotxCount} IOTX price points`)
    
    // Download SOL prices for last 2 days (every 1 minute)
    console.log('\n📈 Downloading SOL prices (1-minute intervals, last 2 days)...')
    const solCount = await PriceDataService.downloadRecentPrices('SOLUSDT', 2, 1)
    console.log(`✅ Downloaded ${solCount} SOL price points`)
    
    // Show cache stats
    console.log('\n📊 Cache Statistics:')
    const stats = await PriceDataService.getCacheStats()
    for (const [symbol, count] of Object.entries(stats)) {
      console.log(`  ${symbol}: ${count} data points`)
    }
    
    console.log('\n🎉 1-minute price data download completed successfully!')
    console.log('💡 This provides much better accuracy for arbitrage analysis!')
    
  } catch (error) {
    console.error('❌ Error downloading prices:', error)
    process.exit(1)
  }
}

download1MinPrices()
