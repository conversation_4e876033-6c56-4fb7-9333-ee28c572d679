// Test Binance API response format
async function testBinanceAPI() {
  try {
    // Test current price
    console.log('Testing Binance API...')
    
    const url = 'https://api.binance.com/api/v3/klines?symbol=IOTXUSDT&interval=1h&limit=3'
    console.log('URL:', url)
    
    const response = await fetch(url)
    console.log('Response status:', response.status)
    
    if (!response.ok) {
      console.error('API error:', response.statusText)
      return
    }
    
    const data = await response.json()
    console.log('Raw response:', JSON.stringify(data, null, 2))
    
    if (data.length > 0) {
      const kline = data[0]
      console.log('\nFirst kline structure:')
      console.log('Array length:', kline.length)
      console.log('Elements:', kline)
      
      // Binance klines return arrays, not objects
      if (Array.isArray(kline)) {
        console.log('\nParsed data:')
        console.log('Open time:', new Date(kline[0]))
        console.log('Open price:', kline[1])
        console.log('High price:', kline[2])
        console.log('Low price:', kline[3])
        console.log('Close price:', kline[4])
        console.log('Volume:', kline[5])
        console.log('Close time:', new Date(kline[6]))
      }
    }
    
  } catch (error) {
    console.error('Error:', error)
  }
}

testBinanceAPI()
