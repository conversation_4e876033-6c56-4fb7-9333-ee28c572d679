// Download price data for specific date range to cover transaction timestamps
import { PriceDataService } from './src/services/priceDataService.ts'

async function downloadMorePrices() {
  console.log('Downloading price data for transaction date range...')
  
  try {
    // Transaction timestamp: 1753983775 = 2025-07-31T17:42:55.000Z
    // Let's download data from July 25 to August 5, 2025
    
    const startDate = new Date('2025-07-25T00:00:00Z')
    const endDate = new Date('2025-08-05T00:00:00Z')
    
    const startTimestamp = Math.floor(startDate.getTime() / 1000)
    const endTimestamp = Math.floor(endDate.getTime() / 1000)
    
    console.log(`Downloading from ${startDate.toISOString()} to ${endDate.toISOString()}`)
    console.log(`Timestamp range: ${startTimestamp} to ${endTimestamp}`)
    
    // Download IOTX prices (every 30 minutes to get better coverage)
    console.log('\n📈 Downloading IOTX prices...')
    const iotxCount = await PriceDataService.downloadPriceRange('IOTXUSDT', startTimestamp, endTimestamp, 30)
    console.log(`✅ Downloaded ${iotxCount} IOTX price points`)
    
    // Download SOL prices (every 30 minutes)
    console.log('\n📈 Downloading SOL prices...')
    const solCount = await PriceDataService.downloadPriceRange('SOLUSDT', startTimestamp, endTimestamp, 30)
    console.log(`✅ Downloaded ${solCount} SOL price points`)
    
    // Show cache stats
    console.log('\n📊 Updated Cache Statistics:')
    const stats = await PriceDataService.getCacheStats()
    for (const [symbol, count] of Object.entries(stats)) {
      console.log(`  ${symbol}: ${count} data points`)
    }
    
    console.log('\n🎉 Extended price download completed successfully!')
    
  } catch (error) {
    console.error('❌ Error downloading prices:', error)
    process.exit(1)
  }
}

downloadMorePrices()
