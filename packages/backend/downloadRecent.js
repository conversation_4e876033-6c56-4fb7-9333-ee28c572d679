// Download recent 2 days of real price data
import { PriceDataService } from './src/services/priceDataService.ts'

async function downloadRecentPrices() {
  console.log('Downloading recent 2 days of price data...')
  
  try {
    // Download IOTX prices for last 2 days (every 15 minutes for better coverage)
    console.log('\n📈 Downloading IOTX prices (last 2 days)...')
    const iotxCount = await PriceDataService.downloadRecentPrices('IOTXUSDT', 2, 15)
    console.log(`✅ Downloaded ${iotxCount} IOTX price points`)
    
    // Download SOL prices for last 2 days (every 15 minutes)
    console.log('\n📈 Downloading SOL prices (last 2 days)...')
    const solCount = await PriceDataService.downloadRecentPrices('SOLUSDT', 2, 15)
    console.log(`✅ Downloaded ${solCount} SOL price points`)
    
    // Show cache stats
    console.log('\n📊 Cache Statistics:')
    const stats = await PriceDataService.getCacheStats()
    for (const [symbol, count] of Object.entries(stats)) {
      console.log(`  ${symbol}: ${count} data points`)
    }
    
    console.log('\n🎉 Recent price download completed successfully!')
    
  } catch (error) {
    console.error('❌ Error downloading prices:', error)
    process.exit(1)
  }
}

downloadRecentPrices()
